<?php
    session_start();
?>

<html>
    <head>
        <title>7-8-1</title>
    </head>

    <body>
        <h1>Employee Information System</h1>
        <form method="post" action="<?php echo $_SERVER['PHP_SELF']; ?>">
            Please type your ID:
            <input type="text" name="id"><br/>
            Please type your password:
            <input type="password" name="passwd"><br/>
            <input type="submit" name="submit" value="See my information">
        </form>
        <hr/>
        Want to sign up, click <a href="7-8-3.php">here</a>.<br/>
        If you are the boss, click <a href="7-10-1.php">here</a> to enter.<br/>
        Forget your password? click <a href="7-8-2.php">here</a> to reset.<br/>
        <hr>

        <?php
            require_once("My-DB-Functions.php");

            if (isset($_REQUEST["submit"]))
            {
                if (!empty($_REQUEST["id"]))
                {
                    $conn = My_Connect_DB();
                    $sql = "SELECT * FROM Employee WHERE id='".$_REQUEST["id"]."' AND passwd='".md5($_REQUEST["passwd"])."';";
                    $result = My_SQL_EXE($conn, $sql);

                    if (mysqli_num_rows($result) <= 0) // empty result
                        echo "Your ID or password is wrong! Try again! <br>";
                    else
                    {
                        $_SESSION["id"] = $_REQUEST["id"];
                        Show_Me($conn, $_REQUEST["id"]);

                        echo "Click <a href='7-10-2'>here</a> to change your password. <br>";
                        echo "Click <a href='7-10-3'>here</a> to log off. <br>";
                    }
                    My_Disconnect_DB($conn);
                }
            }

            function Show_Me($conn, $id)
            {
                $sql = "SELECT * FROM Employee WHERE id='".$id."';";
                $result = My_SQL_EXE($conn, $sql);

                if ($row = mysqli_fetch_row($result))
                {
                    echo "Your signup info is listed as follows. <br>";
                    echo "ID: ".$row[0]."<br>";
                    echo "Password: ******** <br>";
                    echo "Name: ".$row[2]."<br>";
                    echo "Email: ".$row[6]."<br>";
                    echo "Salary: ".$row[3]."<br>";
                    echo "Bonus: ".$row[4]."<br>";
                    echo "<img src='".$row[5]."' width=200 height=200> <br>";
                }
            }
        ?>
    </body>
</html>