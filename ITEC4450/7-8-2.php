<html>
    <head>
        <title>7-8-2</title>
    </head>

    <body>
        <h1>Reset your password:</h1>
        <form method="post" action="<?php echo $_SERVER['PHP_SELF']; ?>">
            <input type="radio" name=resetby value="id" checked>ID <br/>
            <input type="radio" name=resetby value="email" checked>Email <br/>
            Type your ID or Email here:<input type="text" name="idORemail"><br/>
            <input type="submit" name="submit" value="reset passwd">
        </form>

        <?php
            if (isset($_REQUEST["submit"]))
            {
                require_once("My-DB-Functions.php");
                $conn = My_Connect_DB();
                $sql = "SELECT * FROM Employee WHERE ".$_REQUEST["resetby"]."='".$_REQUEST["idORemail"]."';";
                $result = My_SQL_EXE($conn, $sql);
                $row = mysqli_fetch_row($result);

                if ($row)
                {
                    $to = $row[6];
                    $subject = "Reset your password!!!";
                    $msg = "You have requested to reset your password.\r\n".
                            "Your login ID is: ".$row[0]."\r\n".
                            "If it is not you, please disregard this email.".
                            "Otherwise, please click the following link to reset your password.\r\n".
                            "https://mvazquezguzman.domains.ggc.edu/Activities/7-8-4.php?id=".$row[0]."&sand=".md5($row[1]).".";
                    $header = "From: <EMAIL>\r\n CC: <EMAIL>";
                    mail($to, $subject, $msg, $header);
                    echo "To: ".$to."<br>";
                    echo "Please check your email to reset your password. <br>";
                }
                else
                    echo "Your ID or email does not exist. Try again. <br>";
            }
        ?>
    </body>
</html>