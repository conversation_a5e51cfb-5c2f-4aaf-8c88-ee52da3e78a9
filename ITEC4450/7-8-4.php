<?php
    $id = $_GET["id"];
    $sand = $_GET["sand"];

    if ($id != null && $sand != null)
    {
        require_once("My-DB-Functions.php");
        $conn = My_Connect_DB();
        $sql = "SELECT * FROM Employee WHERE id='".$id."';";
        $result = My_SQL_EXE($conn, $sql);
        $row = mysqli_fetch_row($result);

        if ($row)
        {
            if ($sand == md5($row[1]))
            {
?>

<form method="post" action="7-8-5.php">
    <?php
        echo "Your ID is: ".$row[0]."<br>";
        echo "<input type='hidden' name='id' value='".$row[0]."'>";
        echo "<input type='hidden' name='sand' value='".$sand."'>";
    ?>
    Your new password: <input type="password" name="newpw"> <br>
    Confirm your new password: <input type="password" name="newpw2"> <br>
    <input type="submit" name="submit" value="Reset Password">
</form>

<?php
            }
            else
                echo "Wrong sand provided! <br>";
        }
        else
            echo "Wrong id is provided! <br>";
    }
    else
        echo "Wrong URL input! <br>";
?>