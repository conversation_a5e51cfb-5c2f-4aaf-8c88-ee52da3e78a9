<?php
    if ($_POST["newpw"] != $_POST["newpw2"])
    {
        echo "New password does not match! <br>";
        die("Password is not changed! <br>");
    }

    $id = $_POST["id"];
    $sand = $_POST["sand"];

    if ($id != null & $sand != null)
    {
        require_once("My-DB-Functions.php");
        $conn = My_Connect_DB();
        $sql = "SELECT * FROM Employee WHERE id='".$id."';";
        $result = My_SQL_EXE($conn, $sql);
        $row = mysqli_fetch_row($result);

        if ($row)
        {
            if ($sand == md5($row[1]))
            {
                $sql = "UPDATE Employee SET passwd='".md5($_POST["newpw"])."' WHERE id='".$id."';";
                My_SQL_EXE($conn, $sql);
                echo "Password has been changed successfully. Please click <a href='7-8-1.php'>here</a> to login. <br>";
                die("");
            }
        }
    }

    die("Something is wrong, password is not changed. <br>");
?>