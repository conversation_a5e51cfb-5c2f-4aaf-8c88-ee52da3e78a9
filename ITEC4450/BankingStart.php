<?php
    session_start();
    date_default_timezone_set("America/New_York");
?>

<html>
    <head>
        <title>Project 3</title>
    </head>

    <body>
        <?php
            if (isset($_POST["login"]))
            {
                $user = $_POST["user"];
                $passwd = $_POST["passwd"];

                if ($user == "admin" && $passwd == "admin")
                {
                    $_SESSION["user"] = $user;
                    $_SESSION["logged_in"] = true;
                    $_SESSION["password"] = $passwd;

                    if (!isset($_SESSION["balance"]))
                    {
                        $_SESSION["balance"] = 0;
                    }
                    if (!isset($_SESSION["transactions"]))
                    {
                        $_SESSION["transactions"] = array();
                    }
                }
                else
                {
                    echo "Your username or password is not correct! Please click <a href='Project3.php'>here</a> to try again.";
                    exit();
                }
            }

            if (!isset($_SESSION["logged_in"]) || $_SESSION["logged_in"] !== true)
            {
                echo "Your session has expired! Please click <a href='Project3.php'>here</a> to login again.";
                exit();
            }

            $user = $_SESSION["user"];
            $message = "";

            if (isset($_POST["submit"]) && isset($_POST["action"]))
            {
                $action = $_POST["action"];

                // Show Balance
                if ($action == "showBalance")
                {
                    $message = "<h3>Balance Info:</h3>";
                    $message .= "Dear customer, your current balance is: $" . number_format($_SESSION["balance"], 2);
                }
                // Deposit
                elseif ($action == "deposit")
                {
                    $amount = floatval($_POST["depositAmount"]);
                    if ($amount > 0)
                    {
                        $_SESSION["balance"] += $amount;
                        $transaction = array(
                            'type' => 'Deposit',
                            'amount' => $amount,
                            'date' => date("M/d/Y"),
                            'time' => date("h:i:sA"),
                            'ip' => $_SERVER['REMOTE_ADDR']
                        );
                        $_SESSION["transactions"][] = $transaction;

                        $message = "<h3>Deposit Info:</h3>";
                        $message .= "Thank you for deposit of $".number_format($amount, 2)." <br>
                                     Your new balance now is : $".number_format($_SESSION["balance"], 2);
                    }
                    else
                    {
                        $message = "<h3>Deposit Info:</h3>";
                        $message .= "The deposit amount must be greater than $0.00. Please try again.";
                    }
                }
                // Withdraw
                elseif ($action == "withdraw")
                {
                    $amount = floatval($_POST["withdrawAmount"]);
                    if ($amount > 0)
                    {
                        if ($amount <= $_SESSION["balance"])
                        {
                            $_SESSION["balance"] -= $amount;
                            $transaction = array(
                                'type' => 'Withdrawal',
                                'amount' => $amount,
                                'date' => date("M/d/Y"),
                                'time' => date("h:i:sA"),
                                'ip' => $_SERVER['REMOTE_ADDR']
                            );
                            $_SESSION["transactions"][] = $transaction;

                            $message = "<h3>Withdrawal Info:</h3>";
                            $message .= "You successfully withdrew $".number_format($amount, 2)." <br>
                                         Your new balance now is: $".number_format($_SESSION["balance"], 2);
                        }
                        else
                        {
                            $message = "<h3>Withdrawal Info:</h3>";
                            $message .= "Insufficient funds, overdraft is not allowed. Please try again.";
                        }
                    }
                    else
                    {
                        $message = "<h3>Withdrawal Info:</h3>";
                        $message .= "The withdrawal amount must be greater than $0.00. Please try again.";
                    }
                }
                // Show Transaction
                elseif ($action == "tranAction")
                {
                    if (empty($_SESSION["transactions"]))
                    {
                        $message = "<h3>Transaction Info:</h3>";
                        $message .= "No transactions available yet.";
                    }
                    else
                    {
                        $message = "<h3>Transaction Info:</h3>";
                        $counter = 1;
                        foreach ($_SESSION["transactions"] as $transaction)
                        {
                            if (is_array($transaction))
                            {
                                $message .= $counter .".
                                ".$transaction['type'].":
                                $".number_format($transaction['amount'], 2).",
                                on ".$transaction['date'].",
                                at ".$transaction['time'].",
                                from ".$transaction['ip']."
                                <br>";
                            }
                            else
                            {
                                $message .= $counter.". ".$transaction."<br>";
                            }
                            $counter++;
                        }
                    }
                }
                // Change Password
                elseif ($action == "changePass")
                {
                    if (isset($_POST["changePassword"]))
                    {
                        $oldPassword = $_POST["oldPassword"];
                        $newPassword = $_POST["newPassword"];
                        $retypePassword = $_POST["retypePassword"];

                        if ($oldPassword != $_SESSION["password"])
                        {
                            $message = "<h3>Change your password:</h3>";
                            $message .= "Old password is incorrect. Please try again.";
                        }
                        elseif (empty($newPassword))
                        {
                            $message = "<h3>Change your password:</h3>";
                            $message .= "New password cannot be empty. Please try again.";
                        }
                        elseif ($newPassword != $retypePassword)
                        {
                            $message = "<h3>Change your password:</h3>";
                            $message .= "New passwords do not match. Please try again.";
                        }
                        else
                        {
                            $_SESSION["password"] = $newPassword;
                            $message = "<h3>Change your password:</h3>";
                            $message .= "Your password has been changed successfully!";
                        }
                    }
                    else
                    {
                        $message = "<h3>Change your password:</h3>";
                        $message .= "<form method='post' action='".$_SERVER['PHP_SELF']."'>";
                        $message .= "<input type='hidden' name='action' value='changePass'>";
                        $message .= "<input type='hidden' name='submit' value='Submit'>";
                        $message .= "Old Password: <input type='password' name='oldPassword' required><br><br>";
                        $message .= "New Password: <input type='password' name='newPassword' required><br><br>";
                        $message .= "Re-type New Password: <input type='password' name='retypePassword' required><br><br>";
                        $message .= "<input type='submit' name='changePassword' value='Change Password'>";
                        $message .= "</form>";
                    }
                }
                // Logout
                elseif ($action == "logout")
                {
                    echo "<div style='background-color:pink; border:red solid 1px; width:75%; margin:auto; padding: 10px'>";
                    echo "<h3>Logout Info:</h3>";
                    echo "You logged out successfully! <br>";
                    echo "Please click <a href='Project3.php'>here</a> to login again. <br>";
                    echo "Just a friendly reminder that this is a fake bank system. When you log out, your deposit will be reset to 0 and all your transactions will be cleared. <br>";
                    echo "</div>";
                    session_destroy();
                    exit();
                }
            }

            echo "<div style='background-color:pink; border:red solid 1px; width:75%; margin:auto;'>";
            if (!empty($message))
            {
                echo $message;
            }
            else
            {
                echo "Welcome ".$user."<br>";
                $now = time();
                echo "Now is ".date("F j, Y", $now)." ".date("h:i:s A", $now)." <br>";
            }
            echo "</div>";
            echo "<hr>";

            echo "<div style='background-color:lightblue; border:red solid 1px; width:75%; margin:auto;'>";
            echo "Choose what you want to do based on the following menu: <br>";
            echo "<hr>";
            echo "<form method='post' action='".$_SERVER['PHP_SELF']."'>";
            echo "<input type='radio' name='action' value='showBalance' required> Show Balance <br>";
            echo "<input type='radio' name='action' value='deposit' required>     Deposit this amount: <input type='number' name='depositAmount' placeholder='0.00' step='0.01' min='0.01'> <br>";
            echo "<input type='radio' name='action' value='withdraw' required>    Withdraw this amount: <input type='number' name='withdrawAmount' placeholder='0.00' step='0.01' min='0.01'> <br>";
            echo "<input type='radio' name='action' value='tranAction' required>  Show my transactions <br>";
            echo "<input type='radio' name='action' value='changePass' required>  Change my password <br>";
            echo "<input type='radio' name='action' value='logout' required>      Log Out <br>";
            echo "<hr>";
            echo "<input type='submit' name='submit' value='Submit'>";
            echo "</form>";
            echo "</div>";
        ?>
    </body>
</html>