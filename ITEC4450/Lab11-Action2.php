<?php
    session_start();

    if (!isset($_SESSION["userid"]))
    {
        header("Location: Lab11.php");
        exit();
    }
?>

<html>
    <head>
        <title>Lab 11</title>
    </head>

    <body>
        <?php
            require_once("My-DB-Functions.php");

            if (isset($_POST["submit"]))
            {
                $name = $_POST["name"];
                $email = $_POST["email"];

                if (empty($name))
                {
                    echo "Name is required! <br>";
                    echo "Click <a href='Lab11-Action1.php'>here</a> to go back. <br>";
                }
                elseif (empty($email))
                {
                    echo "Email is required! <br>";
                    echo "Click <a href='Lab11-Action1.php'>here</a> to go back. <br>";
                }
                else
                {
                    $tagName = "myfile";
                    $fileAllowed = "PNG:JPEG:JPG:GIF:BMP";
                    $sizeAllowed = 5000000;
                    $overwriteAllowed = 1;
                    $file = UploadFile($tagName, $fileAllowed, $sizeAllowed, $overwriteAllowed);

                    if ($file != false)
                    {
                        echo "<img src='".$file."' width=250 height=250> <br>";
                    }
                    else
                    {
                        echo "File upload failed! <br>";
                    }

                    echo "Thank you for submitting the information. <br>";

                    echo "Please click <a href='Lab11-Action3.php?name=".$name."&email=".$email;
                    if ($file != false)
                    {
                        echo "&file=".$file;
                    }
                    echo "'>here</a> to send an email to verify your information. <br>";
                }
            }
            else
            {
                echo "No data submitted. <br>";
                echo "Click <a href='Lab11-Action1.php'>here</a> to go back. <br>";
            }
        ?>
    </body>
</html>