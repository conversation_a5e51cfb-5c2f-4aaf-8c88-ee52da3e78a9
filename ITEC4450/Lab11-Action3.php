<?php
    session_start();

    if (!isset($_SESSION["userid"]))
    {
        header("Location: Lab11.php");
        exit();
    }
?>

<html>
    <head>
        <title>Lab 11</title>
    </head>

    <body>
        <?php
            if (isset($_GET["name"]) && isset($_GET["email"]))
            {
                $name = $_GET["name"];
                $email = $_GET["email"];
                $file = isset($_GET["file"]) ? $_GET["file"] : "";

                if ($file)
                {
                    echo "<img src='".$file."' width=300 height=300> <br>";
                }
                echo "Your name is: ".$name."<br>";
                echo "Your email is: ".$email."<br>";

                $to = $email;
                $subject = "Matilda's Lab 11";
                $msg = "Dear ".$name.",\r\n\r\n".
                       "Congratulations!!! Lab 11 is almost done.\r\n\r\n".
                       "Thanks,\r\n".
                       "<PERSON> Vazquez-Guzman";
                $header = "From: m<PERSON><PERSON><PERSON>@itec4450.com\r\n CC: <EMAIL>";
                mail($to, $subject, $msg, $header);

                echo "An email has been sent to you via ".$email.". Please check. <br>";

                echo "Click <a href='Lab11-Action4.php?name=".$name."&email=".$email;
                if ($file)
                {
                    echo "&file=".$file;
                }
                echo "'>here</a> to log off and go back to the login page. <br>";
            }
            else
            {
                echo "Missing information. <br>";
                echo "Click <a href='Lab11-Action1.php'>here</a> to go back. <br>";
            }
        ?>
    </body>
</html>