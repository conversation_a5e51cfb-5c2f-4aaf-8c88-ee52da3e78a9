<?php
    session_start();

    if (isset($_REQUEST["submit"]))
    {
        if ($_POST["userid"] == "admin" && $_POST["passwd"] == "admin")
        {
            $_SESSION["userid"] = $_POST["userid"];
            header("Location: Lab11-Action1.php");
            exit();
        }
    }
?>

<html>
    <head>
        <title>Lab 11</title>
    </head>

    <body>
        <form method="post" action="<?php echo $_SERVER['PHP_SELF']; ?>">
            Please type your username: <input type="text" name="userid"><br/>
            Please type your password: <input type="password" name="passwd"><br/>
            <input type="submit" value="log in" name="submit">
        </form>

        <?php
            if (isset($_REQUEST["submit"]) && !($_POST["userid"] == "admin" && $_POST["passwd"] == "admin"))
            {
                echo "Invalid username or password. Please try again.<br>";
            }
        ?>
    </body>
</html>