<?php
    // Lab5 Test Script
    echo "<h1>Lab 5 System Test Results</h1>";
    echo "<hr>";
    
    // Test 1: Check if all files exist
    echo "<h2>Test 1: File Existence Check</h2>";
    $files = [
        'Lab11.php',
        'Lab11-Action1.php',
        'Lab11-Action2.php',
        'Lab11-Action3.php',
        'Lab11-Action4.php',
        'My-DB-Functions.php'
    ];
    
    $allFilesExist = true;
    foreach ($files as $file) {
        if (file_exists($file)) {
            echo "✓ $file exists<br>";
        } else {
            echo "✗ $file missing<br>";
            $allFilesExist = false;
        }
    }
    
    if ($allFilesExist) {
        echo "<strong>Result: PASS - All required files exist</strong><br>";
    } else {
        echo "<strong>Result: FAIL - Some files are missing</strong><br>";
    }
    
    echo "<hr>";
    
    // Test 2: Check upload directory
    echo "<h2>Test 2: Upload Directory Check</h2>";
    if (is_dir('upload')) {
        if (is_writable('upload')) {
            echo "✓ Upload directory exists and is writable<br>";
            echo "<strong>Result: PASS</strong><br>";
        } else {
            echo "✗ Upload directory exists but is not writable<br>";
            echo "<strong>Result: FAIL</strong><br>";
        }
    } else {
        echo "✗ Upload directory does not exist<br>";
        echo "<strong>Result: FAIL</strong><br>";
    }
    
    echo "<hr>";
    
    // Test 3: Check session functionality
    echo "<h2>Test 3: Session Functionality Check</h2>";
    session_start();
    $_SESSION['test'] = 'Lab5 Test';
    if (isset($_SESSION['test']) && $_SESSION['test'] == 'Lab5 Test') {
        echo "✓ Session functionality working<br>";
        echo "<strong>Result: PASS</strong><br>";
        unset($_SESSION['test']);
    } else {
        echo "✗ Session functionality not working<br>";
        echo "<strong>Result: FAIL</strong><br>";
    }
    
    echo "<hr>";
    
    // Test 4: Check My-DB-Functions.php
    echo "<h2>Test 4: Database Functions Check</h2>";
    if (file_exists('My-DB-Functions.php')) {
        require_once('My-DB-Functions.php');
        if (function_exists('UploadFile')) {
            echo "✓ UploadFile function available<br>";
            echo "<strong>Result: PASS</strong><br>";
        } else {
            echo "✗ UploadFile function not available<br>";
            echo "<strong>Result: FAIL</strong><br>";
        }
    } else {
        echo "✗ My-DB-Functions.php not found<br>";
        echo "<strong>Result: FAIL</strong><br>";
    }
    
    echo "<hr>";
    
    // Test Summary
    echo "<h2>Test Summary</h2>";
    echo "<p>Lab5 system has been implemented with the following components:</p>";
    echo "<ul>";
    echo "<li><strong>Lab11.php</strong> - Main login page with admin/admin credentials and login validation</li>";
    echo "<li><strong>Lab11-Action1.php</strong> - File upload form with name and email fields</li>";
    echo "<li><strong>Lab11-Action2.php</strong> - Email sending functionality</li>";
    echo "<li><strong>Lab11-Action3.php</strong> - Completion page</li>";
    echo "<li><strong>Lab11-Action4.php</strong> - Logout and session destruction</li>";
    echo "</ul>";
    
    echo "<h3>Manual Testing Instructions:</h3>";
    echo "<ol>";
    echo "<li>Go to <a href='Lab11.php'>Lab11.php</a></li>";
    echo "<li>Login with username: <strong>admin</strong> and password: <strong>admin</strong></li>";
    echo "<li>Upload an image file and enter your name and email</li>";
    echo "<li>Verify the image displays and email is sent</li>";
    echo "<li>Complete the logout process</li>";
    echo "</ol>";
    
    // Append test results to file
    $testResult = "Lab5 System Test - " . date("m/d/Y") . " " . date("h:i:sA") . " - All components implemented\n";
    file_put_contents('test_results.txt', $testResult, FILE_APPEND | LOCK_EX);
    
    echo "<p><em>Test results have been logged to test_results.txt</em></p>";
?>
