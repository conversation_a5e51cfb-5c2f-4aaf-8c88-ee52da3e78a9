<?php
    function My_Connect_DB()
    {
        $server = "localhost";
        $user = "mvazquez_matvazguz";
        $password = "?_SPl^R*^9.F";
        $dbname = "mvazquez_itec4450";

        $conn = mysqli_connect($server, $user, $password, $dbname);

        if (!$conn)
            die("Connection to Database failed: ".mysqli_connect_error()."<br>");
        else return $conn;
    }

    function My_SQL_EXE($conn, $sql)
    {
        $result = mysqli_query($conn, $sql);

        if ($result)
            echo "SQL is done successfully! <br>";
        else
            echo "Error in running sql: ".$sql." with error: ".mysqli_error($conn)."<br>";
        return $result;
    }

    function My_Disconnect_DB($conn)
    {
        mysqli_close($conn);
    }

    function Run_Select_Show_Results($conn, $sql) // here the sql statement must be a SELECT sql statement
    {
        $result = My_SQL_EXE($conn, $sql);

        echo "<table border='1'>";
            echo "<tr>";
                while ($fieldinfo = mysqli_fetch_field($result))
                {
                    echo "<th>";
                        echo $fieldinfo->name;
                    echo "</th>";
                }
            echo "</tr>";

            while ($row = mysqli_fetch_assoc($result))
            {
                echo "<tr>";
                    foreach ($row as $key=>$value)
                        echo "<td>".$value."</td>";
                echo "</tr>";
            }
        echo "</table>";
        echo "Total rows: ".mysqli_num_rows($result).'<br>';
    }

    function Run_SQL_Show_Result($conn, $sql, $table)
    {
        My_SQL_EXE($conn, $sql);
        $sql = "SELECT * FROM ".$table.";";
        Run_Select_Show_Results($conn, $sql);
    }

    function UploadFile($tagName, $fileAllowed, $sizeAllowed, $overwriteAllowed)
    {
        $uploadOK = 1;
        $dir = "upload/";

        // Check if file was uploaded
        if (!isset($_FILES[$tagName]) || $_FILES[$tagName]['error'] !== UPLOAD_ERR_OK) {
            $uploadOK = 0;
            echo "File upload error occurred.<br/>";
            return false;
        }

        // Create upload directory if it doesn't exist
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0755, true)) {
                $uploadOK = 0;
                echo "Failed to create upload directory.<br/>";
                return false;
            }
        }

        // Clean the filename to remove spaces and special characters
        $originalName = basename($_FILES[$tagName]["name"]);
        $cleanName = preg_replace('/[^a-zA-Z0-9._-]/', '_', $originalName);
        $file = $dir . $cleanName;

        $fileType = pathinfo($file, PATHINFO_EXTENSION);
        $fileSize = $_FILES[$tagName]["size"];

        if($fileSize > $sizeAllowed)
        {
            $uploadOK = 0;
            echo "File is too large<br/>";
        }
        if(!stristr($fileAllowed, $fileType))
        {
            $uploadOK = 0;
            echo "File type is not allowed<br/>";
        }
        if(file_exists($file) && !$overwriteAllowed)
        {
            $uploadOK = 0;
            echo "File already exists and not allow overwritten.<br/>";
        }
        if($uploadOK == 1)
        {
            if(!move_uploaded_file($_FILES[$tagName]["tmp_name"], $file))
            {
                $uploadOK = 0;
                echo "Upload failed in the processing of uploading. Check directory permissions.<br/>";
            }
        }
        if($uploadOK == 1)
            return $file;
        else
            return false;
    }
?>