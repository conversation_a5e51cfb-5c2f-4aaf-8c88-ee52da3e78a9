<?php
    require_once("My-DB-Functions2.php");

    if (isset($_REQUEST["submit1"]))
    {
        if (!empty($_REQUEST["name"]))
        {
            $name = $_REQUEST["name"];
            $type = $_REQUEST["type"];
            $amount = $_REQUEST["amount"];

            if (is_numeric($amount) && $amount > 0 && $amount <= 999999)
            {
                $conn = My_Connect_DB();
                $sql = "INSERT INTO EmployeePayments(name, type, amount)
                        VALUES('".$name."', '".$type."', ".$amount.");";
                My_SQL_EXE($conn, $sql);
                My_Disconnect_DB($conn);
                
                header("Location: Project4.php?success=1");
                exit();
            }
            else
            {
                header("Location: Project4.php?error=amount");
                exit();
            }
        }
        else
        {
            header("Location: Project4.php?error=name");
            exit();
        }
    }
    else
    {
        header("Location: Project4.php");
        exit();
    }
?>
