<html>
    <head>
        <title>Project 4</title>
    </head>

    <body>
        <h1>Employee Payment System</h1>
        <form method="post" action="Project4.php">
            Employee Name:  <input type="text" name="name"><font color=red>*</font>
            <br><br>
            Payment Type:
            <select name="type">
                <option value="salary">Salary</option>
                <option value="bonus">Bonus</option>
                <option value="other">Other</option>
            </select>
            <br><br>
            Payment Amount: <input type="text" name="amount" maxlength=6><font color=red>(max 999999)</font>
            <br><br>
            <input type="submit" name="submit1" value="submit"><br>
        </form>
        <hr/>

        <form method="POST" action="Project4.php">
            <table style="background-color:pink;margin:auto;" border=0>
                <thead align="center"><tr><td colspan="2">Display information in different ways</td></tr></thead>
                <tr>    <td colspan=2><hr/></td><td>
                <tr>    <td align=right><input type=radio name="sortby" value="bynoway"></td>   <td>Display information in its orignal way</td></tr>
                <tr>    <td align=right><input type=radio name="sortby" value="byname"></td>    <td>Sort by name in ascending order</td></tr>
                <tr>    <td align=right><input type=radio name="sortby" value="byone"></td>     <td>Find someone makes how much money (name: <input type="text" name="name">)</td></tr>
                <tr>    <td align=right><input type=radio name="sortby" value="bytotal"></td>   <td>Find each one makes how much money in total and sort results in descending order</td></tr>
                <tr>    <td align=right><input type=radio name="sortby" value="bytype"></td>    <td>Find payments for different types (type: <select name="type">
                            <option value="salary">Salary</option>
                            <option value="bonus">Bonus</option>
                            <option value="other">Other</option>
                        </select>) </td></tr>
                <tr>    <td align=right><input type=radio name="sortby" value="bymax"></td>     <td>Find the employee with highest bonus</td></tr>
                <tr>    <td align=right><input type=radio name="sortby" value="bycat"></td>     <td>Find the total payments for the 3 catagories</td></tr>
                <tr>    <td colspan=2 align="center"><input name="submit2" alt="Login" type="submit" value="display"></td></tr>
            </table>
        </form>
        <hr/>

        <?php
            require_once("My-DB-Functions2.php");

            $conn = My_Connect_DB();

            $sql = "CREATE TABLE IF NOT EXISTS EmployeePayments(
                                    name VARCHAR(50) NOT NULL,
                                    type VARCHAR(20) NOT NULL,
                                    amount DECIMAL(10,2) NOT NULL,
                                    paymentID INT(6) UNSIGNED AUTO_INCREMENT PRIMARY KEY
                                );";
            My_SQL_EXE($conn, $sql);

            $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM EmployeePayments");
            if ($result)
            {
                $row = mysqli_fetch_assoc($result);
                if ($row['count'] == 0)
                {
                    $sql = "INSERT INTO EmployeePayments (name, type, amount) VALUES('Mike', 'salary', 1200);";
                    My_SQL_EXE($conn, $sql);
                    $sql = "INSERT INTO EmployeePayments (name, type, amount) VALUES('Mike', 'bonus', 500);";
                    My_SQL_EXE($conn, $sql);
                    $sql = "INSERT INTO EmployeePayments (name, type, amount) VALUES('Will', 'other', 100);";
                    My_SQL_EXE($conn, $sql);
                    $sql = "INSERT INTO EmployeePayments (name, type, amount) VALUES('Joseph', 'salary', 1500);";
                    My_SQL_EXE($conn, $sql);
                    $sql = "INSERT INTO EmployeePayments (name, type, amount) VALUES('Caesar', 'bonus', 800);";
                    My_SQL_EXE($conn, $sql);
                    $sql = "INSERT INTO EmployeePayments (name, type, amount) VALUES('Lisa', 'bonus', 300);";
                    My_SQL_EXE($conn, $sql);
                    $sql = "INSERT INTO EmployeePayments (name, type, amount) VALUES('Jolyne', 'salary', 1100);";
                    My_SQL_EXE($conn, $sql);
                    $sql = "INSERT INTO EmployeePayments (name, type, amount) VALUES('Erina', 'other', 250);";
                    My_SQL_EXE($conn, $sql);
                    $sql = "INSERT INTO EmployeePayments (name, type, amount) VALUES('Jonathan', 'salary', 1300);";
                    My_SQL_EXE($conn, $sql);
                    $sql = "INSERT INTO EmployeePayments (name, type, amount) VALUES('Tom', 'bonus', 600);";
                    My_SQL_EXE($conn, $sql);
                }
            }

            if (isset($_REQUEST["submit1"]))
            {
                if (!empty($_REQUEST["name"]))
                {
                    $name = $_REQUEST["name"];
                    $type = $_REQUEST["type"];
                    $amount = $_REQUEST["amount"];

                    if (is_numeric($amount) && $amount > 0 && $amount <= 999999)
                    {
                        $sql = "INSERT INTO EmployeePayments(name, type, amount)
                                VALUES('".$name."', '".$type."', ".$amount.");";
                        echo "<div style='text-align: center;'>";
                        Run_SQL_Show_Result($conn, $sql, "EmployeePayments");
                        echo "</div>";
                    }
                    else
                    {
                        echo "<p style='color:red;'>Please enter a valid amount (1-999999)!</p>";
                    }
                }
                else
                {
                    echo "<p style='color:red;'>Employee name is required!</p>";
                }
            }
            else if (isset($_REQUEST["submit2"]))
            {
                if (isset($_REQUEST["sortby"]))
                {
                    $sortby = $_REQUEST["sortby"];

                    if ($sortby == "bynoway")
                    {
                        $sql = "SELECT * FROM EmployeePayments ORDER BY paymentID;";
                        echo "<h3>Information in Original Way:</h3>";
                        echo "<div style='text-align: center;'>";
                        Run_Select_Show_Results($conn, $sql);
                        echo "</div>";
                    }
                    else if ($sortby == "byname")
                    {
                        $sql = "SELECT * FROM EmployeePayments ORDER BY name ASC;";
                        echo "<h3>Sorted by Name (Ascending):</h3>";
                        echo "<div style='text-align: center;'>";
                        Run_Select_Show_Results($conn, $sql);
                        echo "</div>";
                    }
                    else if ($sortby == "byone")
                    {
                        if (!empty($_REQUEST["name"]))
                        {
                            $name = $_REQUEST["name"];
                            $sql = "SELECT * FROM EmployeePayments WHERE name='".$name."';";
                            echo "<h3>Payments for ".$name.":</h3>";
                            echo "<div style='text-align: center;'>";
                            Run_Select_Show_Results($conn, $sql);
                            echo "</div>";
                        }
                        else
                        {
                            echo "<p style='color:red;'>Please enter a name to search!</p>";
                        }
                    }
                    else if ($sortby == "bytotal")
                    {
                        $sql = "SELECT name, SUM(amount) as total_amount FROM EmployeePayments GROUP BY name ORDER BY total_amount DESC;";
                        echo "<h3>Total Payments by Employee (Descending):</h3>";
                        echo "<div style='text-align: center;'>";
                        Run_Select_Show_Results($conn, $sql);
                        echo "</div>";
                    }
                    else if ($sortby == "bytype")
                    {
                        $type = $_REQUEST["type"];
                        $sql = "SELECT * FROM EmployeePayments WHERE type='".$type."';";
                        echo "<h3>Payments for Type: ".$type."</h3>";
                        echo "<div style='text-align: center;'>";
                        Run_Select_Show_Results($conn, $sql);
                        echo "</div>";
                    }
                    else if ($sortby == "bymax")
                    {
                        $sql = "SELECT * FROM EmployeePayments WHERE type='bonus' AND amount = (SELECT MAX(amount) FROM EmployeePayments WHERE type='bonus');";
                        echo "<h3>Employee with Highest Bonus:</h3>";
                        echo "<div style='text-align: center;'>";
                        Run_Select_Show_Results($conn, $sql);
                        echo "</div>";
                    }
                    else if ($sortby == "bycat")
                    {
                        $sql = "SELECT type, SUM(amount) as total_amount FROM EmployeePayments GROUP BY type;";
                        echo "<h3>Total Payments by Category:</h3>";
                        echo "<div style='text-align: center;'>";
                        Run_Select_Show_Results($conn, $sql);
                        echo "</div>";
                    }
                }
                else
                {
                    echo "<p style='color:red;'>Please select a display option!</p>";
                }
            }

            if (!isset($_REQUEST["submit1"]) && !isset($_REQUEST["submit2"])) {
                $sql = "SELECT * FROM EmployeePayments ORDER BY paymentID;";
                echo "<h3>Information in Original Way:</h3>";
                echo "<div style='text-align: center;'>";
                Run_Select_Show_Results($conn, $sql);
                echo "</div>";
            }

            My_Disconnect_DB($conn);
        ?>
    </body>
</html>