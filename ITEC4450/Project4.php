<?php
    require_once("My-DB-Functions2.php");

    $conn = My_Connect_DB();

    $sql = "CREATE TABLE IF NOT EXISTS EmployeePayments(
                            name VARCHAR(50) NOT NULL,
                            type VARCHAR(20) NOT NULL,
                            amount DECIMAL(10,2) NOT NULL,
                            paymentID INT(6) UNSIGNED AUTO_INCREMENT PRIMARY KEY
                        );";
    My_SQL_EXE($conn, $sql);

    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM EmployeePayments");
    if ($result)
    {
        $row = mysqli_fetch_assoc($result);
        if ($row['count'] == 0)
        {
            $sql = "INSERT INTO EmployeePayments (name, type, amount) VALUES('Mike', 'salary', 1200);";
            My_SQL_EXE($conn, $sql);
            $sql = "INSERT INTO EmployeePayments (name, type, amount) VALUES('Mike', 'bonus', 500);";
            My_SQL_EXE($conn, $sql);
            $sql = "INSERT INTO EmployeePayments (name, type, amount) VALUES('Will', 'other', 100);";
            My_SQL_EXE($conn, $sql);
            $sql = "INSERT INTO EmployeePayments (name, type, amount) VALUES('Bill', 'salary', 1500);";
            My_SQL_EXE($conn, $sql);
            $sql = "INSERT INTO EmployeePayments (name, type, amount) VALUES('Will', 'other', 100);";
            My_SQL_EXE($conn, $sql);
            $sql = "INSERT INTO EmployeePayments (name, type, amount) VALUES('Mike', 'other', 250);";
            My_SQL_EXE($conn, $sql);
            $sql = "INSERT INTO EmployeePayments (name, type, amount) VALUES('Bill', 'bonus', 600);";
            My_SQL_EXE($conn, $sql);
            $sql = "INSERT INTO EmployeePayments (name, type, amount) VALUES('Tracy', 'salary', 2000);";
            My_SQL_EXE($conn, $sql);
            $sql = "INSERT INTO EmployeePayments (name, type, amount) VALUES('Will', 'bonus', 400);";
            My_SQL_EXE($conn, $sql);
            $sql = "INSERT INTO EmployeePayments (name, type, amount) VALUES('Joseph', 'salary', 1500);";
            My_SQL_EXE($conn, $sql);
        }
    }

    if (isset($_POST["submit1"]))
    {
        if (!empty($_POST["name"]))
        {
            $name = $_POST["name"];
            $type = $_POST["type"];
            $amount = $_POST["amount"];

            if (is_numeric($amount) && $amount > 0 && $amount <= 999999)
            {
                $sql = "INSERT INTO EmployeePayments(name, type, amount)
                        VALUES('".$name."', '".$type."', ".$amount.");";
                My_SQL_EXE($conn, $sql);

                header("Location: Project4.php?success=1");
                exit();
            }
            else
            {
                header("Location: Project4.php?error=amount");
                exit();
            }
        }
        else
        {
            header("Location: Project4.php?error=name");
            exit();
        }
    }
?>

<html>
    <head>
        <title>Project 4</title>
    </head>

    <body>
        <h1>Employee Payment System</h1>
        <form method="post" action="<?php echo $_SERVER['PHP_SELF']; ?>">
            Employee Name:  <input type="text" name="name"><font color=red> *</font>
            <br><br>
            Payment Type:
            <select name="type">
                <option value="salary">Salary</option>
                <option value="bonus">Bonus</option>
                <option value="other">Other</option>
            </select>
            <br><br>
            Payment Amount: <input type="text" name="amount" maxlength=6><font color=red> (max 999999)</font>
            <br><br>
            <input type="submit" name="submit1" value="submit"><br>
        </form>
        <hr/>

        <form method="post" action="<?php echo $_SERVER['PHP_SELF']; ?>">
            <table style="background-color:pink;margin:auto;" border=0>
                <thead align="center"><tr><td colspan="2">Display information in different ways</td></tr></thead>
                <tr>    <td colspan=2><hr/></td><td>
                <tr>    <td align=right><input type=radio name="sortby" value="bynoway"></td>   <td>Display information in its orignal way</td></tr>
                <tr>    <td align=right><input type=radio name="sortby" value="byname"></td>    <td>Sort by name in ascending order</td></tr>
                <tr>    <td align=right><input type=radio name="sortby" value="byone"></td>     <td>Find someone makes how much money (name: <input type="text" name="name">)</td></tr>
                <tr>    <td align=right><input type=radio name="sortby" value="bytotal"></td>   <td>Find each one makes how much money in total and sort results in descending order</td></tr>
                <tr>    <td align=right><input type=radio name="sortby" value="bytype"></td>    <td>Find payments for different types (type: <select name="type">
                            <option value="salary">Salary</option>
                            <option value="bonus">Bonus</option>
                            <option value="other">Other</option>
                        </select>) </td></tr>
                <tr>    <td align=right><input type=radio name="sortby" value="bymax"></td>     <td>Find the employee with highest bonus</td></tr>
                <tr>    <td align=right><input type=radio name="sortby" value="bycat"></td>     <td>Find the total payments for the 3 catagories</td></tr>
                <tr>    <td colspan=2 align="center"><input name="submit2" alt="Login" type="submit" value="display"></td></tr>
            </table>
        </form>
        <hr/>

        <?php
            if (isset($_GET["success"]) && $_GET["success"] == "1")
            {
                echo "<p style='color:green; font-weight:bold;'>Payment record added successfully!</p>";
            }
            else if (isset($_GET["error"]))
            {
                if ($_GET["error"] == "name")
                    echo "<p style='color:red;'>Employee name is required, try again! </p>";
                else if ($_GET["error"] == "amount")
                    echo "<p style='color:red;'>Please enter a valid amount (1-999999)!</p>";
            }

            if (isset($_POST["submit2"]))
            {
                if (isset($_POST["sortby"]))
                {
                    $sortby = $_POST["sortby"];

                    if ($sortby == "bynoway")
                    {
                        $sql = "SELECT * FROM EmployeePayments ORDER BY paymentID;";
                        echo "<div align='center'>";
                        echo "Information in Original Way: <br>";
                        Run_Select_Show_Results($conn, $sql);
                        echo "</div>";
                    }
                    else if ($sortby == "byname")
                    {
                        $sql = "SELECT * FROM EmployeePayments ORDER BY name ASC;";
                        echo "<div align='center'>";
                        echo "Sorted by Name (Ascending): <br>";
                        Run_Select_Show_Results($conn, $sql);
                        echo "</div>";
                    }
                    else if ($sortby == "byone")
                    {
                        if (!empty($_POST["name"]))
                        {
                            $name = $_POST["name"];
                            $sql = "SELECT * FROM EmployeePayments WHERE name='".$name."';";
                            echo "<div align='center'>";
                            echo "Payments for ".$name.": <br>";
                            Run_Select_Show_Results($conn, $sql);
                            echo "</div>";
                        }
                        else
                        {
                            echo "<p style='color:red;'>Please enter a name to search!</p>";
                        }
                    }
                    else if ($sortby == "bytotal")
                    {
                        $sql = "SELECT name, SUM(amount) as total FROM EmployeePayments GROUP BY name ORDER BY total_amount DESC;";
                        echo "<div align='center'>";
                        echo "Total Payments by Employee (Descending): <br>";
                        Run_Select_Show_Results($conn, $sql);
                        echo "</div>";
                    }
                    else if ($sortby == "bytype")
                    {
                        $type = $_POST["type"];
                        $sql = "SELECT * FROM EmployeePayments WHERE type='".$type."';";
                        echo "<div align='center'>";
                        echo "Payments for Type: ".$type." <br>";
                        Run_Select_Show_Results($conn, $sql);
                        echo "</div>";
                    }
                    else if ($sortby == "bymax")
                    {
                        $sql = "SELECT name, type, amount FROM EmployeePayments WHERE type='bonus' AND amount = (SELECT MAX(amount) FROM EmployeePayments WHERE type='bonus');";
                        echo "<div align='center'>";
                        echo "Employee with Highest Bonus: <br>";
                        Run_Select_Show_Results($conn, $sql);
                        echo "</div>";
                    }
                    else if ($sortby == "bycat")
                    {
                        $sql = "SELECT type, SUM(amount) as Total Payment FROM EmployeePayments GROUP BY type;";
                        echo "<div align='center'>";
                        echo "Total Payments by Category: <br>";
                        Run_Select_Show_Results($conn, $sql);
                        echo "</div>";
                    }
                }
                else
                {
                    echo "<p style='color:red;'>Please select a display option!</p>";
                }
            }

            if (!isset($_POST["submit2"]) && !isset($_GET["success"]) && !isset($_GET["error"]))
            {
                $sql = "SELECT * FROM EmployeePayments ORDER BY paymentID;";
                echo "<h3>Information in Original Way:</h3>";
                Run_Select_Show_Results($conn, $sql);
            }

            /*
            $sql = "SELECT COUNT(*) as total_records FROM EmployeePayments;";
            $result = mysqli_query($conn, $sql);
            if ($result)
            {
                $row = mysqli_fetch_assoc($result);
                echo "<p><strong>Total number of records in the table: ".$row['total_records']."</strong></p>";
            }
            */

            My_Disconnect_DB($conn);
        ?>
    </body>
</html>